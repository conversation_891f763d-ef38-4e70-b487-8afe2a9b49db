package com.medicalapp.repository;

import com.medicalapp.model.Patient;
import com.medicalapp.model.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;
import java.util.List;

@Repository
public interface PatientRepository extends MongoRepository<Patient, String> {
    
    Optional<Patient> findByUser(User user);
    
    Optional<Patient> findByUserId(String userId);
    
    List<Patient> findByGender(Patient.Gender gender);
    
    List<Patient> findByBloodType(String bloodType);
    
    @Query("{'allergies': {$in: [?0]}}")
    List<Patient> findByAllergiesContaining(String allergy);
    
    @Query("{'chronicConditions': {$in: [?0]}}")
    List<Patient> findByChronicConditionsContaining(String condition);
    
    List<Patient> findByDateOfBirthBetween(LocalDate startDate, LocalDate endDate);
    
    @Query("{'currentMedications.name': {$regex: ?0, $options: 'i'}}")
    List<Patient> findByMedicationName(String medicationName);
    
    @Query("{'user.firstName': {$regex: ?0, $options: 'i'}, 'user.lastName': {$regex: ?1, $options: 'i'}}")
    List<Patient> findByUserFirstNameAndLastName(String firstName, String lastName);
}
