package com.medicalapp.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "patients")
public class Patient {

    @Id
    private String id;

    @DBRef
    private User user;

    // Basic Identification
    private String healthId; // Health ID like 123GIGMTTG2

    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    private Gender gender;

    private String phoneNumber;

    // Physical Measurements
    private Double height; // in cm

    private Double weight; // in kg

    private Double waistCircumference; // in cm

    // Blood and Medical Info
    private String bloodGroup; // A+, B+, O+, AB+, A-, B-, O-, AB-

    private List<String> allergies = new ArrayList<>();

    private SmokingStatus smokingStatus = SmokingStatus.NO;

    // Physical Activity
    private PhysicalActivityLevel physicalActivityLevel;

    private Double metScore; // Exercise tolerance

    // Past Medical Illnesses
    private List<String> pastMedicalIllnesses = new ArrayList<>(); // DM, HTN, DL, CKD, Stroke, IHD

    // References to separate collections
    private String currentVitalSignsId; // Reference to latest VitalSigns

    private String currentLabResultsId; // Reference to latest LabResults

    // Doctor Selection
    private String selectedDoctorId; // Doctor can see patient profile if selected

    private String insuranceProvider;

    private String insurancePolicyNumber;

    private String address;

    private String emergencyContactName;

    private String emergencyContactPhone;

    private String emergencyContactRelation;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Calculated Properties
    public Double getBMI() {
        if (height != null && weight != null && height > 0) {
            double heightInMeters = height / 100.0;
            return weight / (heightInMeters * heightInMeters);
        }
        return null;
    }

    public String getBMICategory() {
        Double bmi = getBMI();
        if (bmi == null) return "Unknown";

        if (bmi < 18.5) return "Underweight";
        else if (bmi < 25) return "Normal";
        else if (bmi < 30) return "Overweight";
        else return "Obese";
    }

    public Double getWaistToHeightRatio() {
        if (waistCircumference != null && height != null && height > 0) {
            return waistCircumference / height;
        }
        return null;
    }

    public boolean isWaistCircumferenceIdeal() {
        if (waistCircumference == null || gender == null) return false;

        return switch (gender) {
            case MALE -> waistCircumference <= 90.0;
            case FEMALE -> waistCircumference <= 80.0;
            default -> false;
        };
    }

    public enum Gender {
        MALE("M"),
        FEMALE("F"),
        OTHER("O"),
        PREFER_NOT_TO_SAY("N");

        private final String code;

        Gender(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    public enum SmokingStatus {
        YES,
        NO,
        FORMER_SMOKER
    }

    public enum PhysicalActivityLevel {
        SEDENTARY("Sedentary"),
        LIGHTLY_ACTIVE("Lightly Active"),
        MODERATELY_ACTIVE("Moderately Active"),
        VERY_ACTIVE("Very Active"),
        EXTREMELY_ACTIVE("Extremely Active");

        private final String description;

        PhysicalActivityLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VitalSigns {
        private Double systolicBP; // mmHg
        private Double diastolicBP; // mmHg
        private Integer pulseRate; // bpm (resting)
        private Integer respiratoryRate; // breaths per minute (resting)
        private LocalDateTime recordedAt;

        public String getBPCategory() {
            if (systolicBP == null || diastolicBP == null) return "Unknown";

            if (systolicBP < 120 && diastolicBP < 80) return "Optimal";
            else if (systolicBP <= 129 && diastolicBP <= 84) return "Normal";
            else if ((systolicBP <= 139 || diastolicBP <= 89)) return "High Normal";
            else if ((systolicBP <= 159 || diastolicBP <= 99)) return "Grade 1 Hypertension (Mild)";
            else if ((systolicBP <= 179 || diastolicBP <= 109)) return "Grade 2 Hypertension";
            else return "Severe Hypertension";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LabResults {
        private Double hemoglobinLevel; // g/dL
        private Double bloodGlucoseLevel; // mg/dL
        private Double ldlCholesterolLevel; // mg/dL
        private Double serumCreatinineLevel; // mg/dL
        private LocalDateTime recordedAt;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Medication {
        private String name;
        private String dosage;
        private String frequency;
        private LocalDate startDate;
        private LocalDate endDate;
        private String prescribedBy;
        private String notes;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MedicalRecord {
        private String id;
        private LocalDate date;
        private String type; // Visit, Lab Result, Procedure, etc.
        private String description;
        private String diagnosis;
        private String treatment;
        private String provider;
        private String facility;
        private List<String> attachments = new ArrayList<>();
        private String notes;
    }
}
