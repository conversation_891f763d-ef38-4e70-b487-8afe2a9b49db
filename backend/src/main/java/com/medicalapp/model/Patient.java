package com.medicalapp.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Past;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "patients")
public class Patient {
    
    @Id
    private String id;
    
    @DBRef
    private User user;
    
    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;
    
    private Gender gender;
    
    private String bloodType;
    
    private Double height; // in cm
    
    private Double weight; // in kg
    
    private String address;
    
    private String emergencyContactName;
    
    private String emergencyContactPhone;
    
    private String emergencyContactRelation;
    
    private List<String> allergies = new ArrayList<>();
    
    private List<String> chronicConditions = new ArrayList<>();
    
    private List<Medication> currentMedications = new ArrayList<>();
    
    private List<MedicalRecord> medicalHistory = new ArrayList<>();
    
    private String insuranceProvider;
    
    private String insurancePolicyNumber;
    
    private String primaryPhysician;
    
    private String primaryPhysicianPhone;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    public enum Gender {
        MALE,
        FEMALE,
        OTHER,
        PREFER_NOT_TO_SAY
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Medication {
        private String name;
        private String dosage;
        private String frequency;
        private LocalDate startDate;
        private LocalDate endDate;
        private String prescribedBy;
        private String notes;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MedicalRecord {
        private String id;
        private LocalDate date;
        private String type; // Visit, Lab Result, Procedure, etc.
        private String description;
        private String diagnosis;
        private String treatment;
        private String provider;
        private String facility;
        private List<String> attachments = new ArrayList<>();
        private String notes;
    }
}
