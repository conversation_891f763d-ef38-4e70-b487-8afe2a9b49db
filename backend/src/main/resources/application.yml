server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: medical-data-backend
  
  data:
    mongodb:
      uri: mongodb://localhost:27017/medical_data_db
      auto-index-creation: true
  
  security:
    user:
      name: admin
      password: admin123
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000 # 24 hours in milliseconds
  refresh-expiration: 604800000 # 7 days in milliseconds

# QR Code Configuration
qr:
  expiration: 3600000 # 1 hour in milliseconds
  size: 300 # QR code size in pixels

# CORS Configuration
cors:
  allowed-origins: 
    - http://localhost:8100
    - http://localhost:4200
    - capacitor://localhost
    - ionic://localhost
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

# Logging Configuration
logging:
  level:
    com.medicalapp: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/medical-app.log

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
