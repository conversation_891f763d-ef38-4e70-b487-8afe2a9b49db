# Medical Data Management System

A comprehensive healthcare record management system that allows patients to manage their medical data and share profiles with doctors via QR codes.

## Architecture Overview

### Technology Stack
- **Frontend**: Ionic Framework (Angular-based mobile app)
- **Backend**: Spring Boot REST API
- **Database**: MongoDB
- **Authentication**: JWT tokens
- **QR Code**: QR code generation and scanning
- **Security**: HIPAA compliant data encryption

### System Components

1. **Mobile App (Ionic)**
   - Patient registration and authentication
   - Medical data entry and management
   - QR code generation for profile sharing
   - Secure data synchronization

2. **Backend API (Spring Boot)**
   - RESTful API endpoints
   - User authentication and authorization
   - Medical data CRUD operations
   - QR code data management
   - Data validation and security

3. **Database (MongoDB)**
   - Patient profiles
   - Medical records
   - User authentication data
   - Audit logs

## Project Structure

```
medical-data-app/
├── backend/                    # Spring Boot API
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/
│   │   │   │       └── medicalapp/
│   │   │   │           ├── MedicalAppApplication.java
│   │   │   │           ├── config/
│   │   │   │           ├── controller/
│   │   │   │           ├── model/
│   │   │   │           ├── repository/
│   │   │   │           ├── service/
│   │   │   │           └── security/
│   │   │   └── resources/
│   │   │       ├── application.yml
│   │   │       └── static/
│   │   └── test/
│   ├── pom.xml
│   └── Dockerfile
├── frontend/                   # Ionic Mobile App
│   ├── src/
│   │   ├── app/
│   │   │   ├── pages/
│   │   │   ├── services/
│   │   │   ├── models/
│   │   │   └── shared/
│   │   ├── assets/
│   │   └── theme/
│   ├── ionic.config.json
│   ├── package.json
│   └── capacitor.config.ts
├── docker-compose.yml          # Development environment
├── docs/                       # Documentation
└── README.md
```

## Features

### Patient Features
- **Registration & Login**: Secure account creation and authentication
- **Profile Management**: Personal and medical information management
- **Medical Records**: Add, view, edit medical history, medications, allergies
- **QR Code Sharing**: Generate QR codes to share profile with healthcare providers
- **Data Security**: End-to-end encryption of sensitive medical data

### Doctor/Healthcare Provider Features
- **QR Code Scanning**: Scan patient QR codes to access shared profiles
- **Patient Data Access**: View authorized patient medical information
- **Secure Communication**: HIPAA-compliant data access

### System Features
- **Cross-platform**: iOS and Android support via Ionic
- **Offline Support**: Local data storage with sync capabilities
- **Audit Logging**: Track all data access and modifications
- **Data Backup**: Secure cloud backup of medical records
- **HIPAA Compliance**: Healthcare data protection standards

## Getting Started

### Prerequisites
- Node.js (v16+)
- Java 17+
- MongoDB
- Ionic CLI
- Docker (optional)

### Development Setup
1. Clone the repository
2. Set up MongoDB database
3. Configure backend application properties
4. Install frontend dependencies
5. Run development servers

## Security Considerations

- JWT-based authentication
- Data encryption at rest and in transit
- HIPAA compliance measures
- Secure QR code generation with expiration
- Role-based access control
- Audit logging for all operations

## License

This project is licensed under the MIT License - see the LICENSE file for details.
