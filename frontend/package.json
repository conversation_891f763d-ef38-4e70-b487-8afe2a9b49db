{"name": "medical-app-frontend", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-linear-gradient": "~12.3.0", "react-native-vector-icons": "^10.0.2", "@expo/vector-icons": "^13.0.0", "axios": "^1.6.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-svg": "13.9.0", "expo-barcode-scanner": "~12.5.3", "expo-sharing": "~11.5.0", "expo-print": "~12.4.4", "react-native-qrcode-svg": "^6.2.0", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.1", "expo-image-picker": "~14.3.2", "expo-document-picker": "~11.5.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}