import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Base URL - Update this to your backend URL
const BASE_URL = 'http://localhost:8080'; // Change to your backend URL

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const token = await AsyncStorage.getItem('token');
        if (token) {
          const refreshResponse = await axios.post(`${BASE_URL}/auth/refresh`, {}, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          const newToken = refreshResponse.data.message.split(': ')[1];
          await AsyncStorage.setItem('token', newToken);
          
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        await AsyncStorage.removeItem('token');
        await AsyncStorage.removeItem('user');
        // You might want to emit an event here to trigger logout in your app
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (username, password) => 
    api.post('/auth/signin', { username, password }),
  
  signup: (userData) => 
    api.post('/auth/signup', userData),
  
  refreshToken: (token) => 
    api.post('/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${token}` }
    }),
};

// Patient API
export const patientAPI = {
  getProfile: () => 
    api.get('/patients/profile'),
  
  updateProfile: (profileData) => 
    api.put('/patients/profile', profileData),
  
  getCurrentVitalSigns: () => 
    api.get('/patients/profile/vitals/current'),
  
  getCurrentLabResults: () => 
    api.get('/patients/profile/labs/current'),
  
  getCurrentMedications: () => 
    api.get('/patients/profile/medications/current'),
  
  getMedicalHistory: () => 
    api.get('/patients/profile/history'),
  
  getPatientById: (id) => 
    api.get(`/patients/${id}`),
  
  getAllPatients: () => 
    api.get('/patients'),
  
  getPatientsByDoctor: (doctorId) => 
    api.get(`/patients/by-doctor/${doctorId}`),
};

// Vital Signs API
export const vitalSignsAPI = {
  create: (vitalSignsData) => 
    api.post('/vital-signs', vitalSignsData),
  
  getByPatient: (patientId) => 
    api.get(`/vital-signs/patient/${patientId}`),
  
  update: (id, vitalSignsData) => 
    api.put(`/vital-signs/${id}`, vitalSignsData),
  
  delete: (id) => 
    api.delete(`/vital-signs/${id}`),
};

// Lab Results API
export const labResultsAPI = {
  create: (labResultsData) => 
    api.post('/lab-results', labResultsData),
  
  getByPatient: (patientId) => 
    api.get(`/lab-results/patient/${patientId}`),
  
  update: (id, labResultsData) => 
    api.put(`/lab-results/${id}`, labResultsData),
  
  delete: (id) => 
    api.delete(`/lab-results/${id}`),
};

// Medication API
export const medicationAPI = {
  create: (medicationData) => 
    api.post('/medications', medicationData),
  
  getByPatient: (patientId) => 
    api.get(`/medications/patient/${patientId}`),
  
  update: (id, medicationData) => 
    api.put(`/medications/${id}`, medicationData),
  
  delete: (id) => 
    api.delete(`/medications/${id}`),
};

// Medical Record API
export const medicalRecordAPI = {
  create: (recordData) => 
    api.post('/medical-records', recordData),
  
  getByPatient: (patientId) => 
    api.get(`/medical-records/patient/${patientId}`),
  
  update: (id, recordData) => 
    api.put(`/medical-records/${id}`, recordData),
  
  delete: (id) => 
    api.delete(`/medical-records/${id}`),
};

export default api;
