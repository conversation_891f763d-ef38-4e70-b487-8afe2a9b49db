import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { patientAPI } from '../services/api';

const PrescriptionScreen = () => {
  const [medications, setMedications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMedications();
  }, []);

  const loadMedications = async () => {
    try {
      const response = await patientAPI.getCurrentMedications();
      setMedications(response.data);
    } catch (error) {
      console.error('Error loading medications:', error);
      Alert.alert('Error', 'Failed to load medications');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMedications();
    setRefreshing(false);
  };

  const getMedicationTypeColor = (type) => {
    switch (type) {
      case 'PRESCRIPTION':
        return '#e74c3c';
      case 'OVER_THE_COUNTER':
        return '#3498db';
      case 'SUPPLEMENT':
        return '#2ecc71';
      case 'HERBAL':
        return '#f39c12';
      case 'VACCINE':
        return '#9b59b6';
      default:
        return '#95a5a6';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Ongoing';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading medications...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Current Medications</Text>
        <Text style={styles.headerSubtitle}>
          {medications.length} active medication{medications.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {medications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="medical-outline" size={80} color="#bdc3c7" />
          <Text style={styles.emptyTitle}>No Current Medications</Text>
          <Text style={styles.emptySubtitle}>
            Your current medications will appear here
          </Text>
        </View>
      ) : (
        medications.map((medication, index) => (
          <MedicationCard
            key={medication.id || index}
            medication={medication}
            typeColor={getMedicationTypeColor(medication.type)}
          />
        ))
      )}
    </ScrollView>
  );
};

const MedicationCard = ({ medication, typeColor }) => (
  <View style={styles.medicationCard}>
    <View style={styles.medicationHeader}>
      <View style={styles.medicationTitleContainer}>
        <Text style={styles.medicationName}>{medication.name}</Text>
        <View style={[styles.typeTag, { backgroundColor: typeColor }]}>
          <Text style={styles.typeText}>
            {medication.type?.replace('_', ' ') || 'MEDICATION'}
          </Text>
        </View>
      </View>
      <Ionicons name="medical" size={24} color={typeColor} />
    </View>

    <View style={styles.medicationDetails}>
      <View style={styles.detailRow}>
        <Ionicons name="flask-outline" size={16} color="#7f8c8d" />
        <Text style={styles.detailLabel}>Dosage:</Text>
        <Text style={styles.detailValue}>{medication.dosage}</Text>
      </View>

      <View style={styles.detailRow}>
        <Ionicons name="time-outline" size={16} color="#7f8c8d" />
        <Text style={styles.detailLabel}>Frequency:</Text>
        <Text style={styles.detailValue}>{medication.frequency}</Text>
      </View>

      {medication.indication && (
        <View style={styles.detailRow}>
          <Ionicons name="information-circle-outline" size={16} color="#7f8c8d" />
          <Text style={styles.detailLabel}>For:</Text>
          <Text style={styles.detailValue}>{medication.indication}</Text>
        </View>
      )}

      {medication.prescribedBy && (
        <View style={styles.detailRow}>
          <Ionicons name="person-outline" size={16} color="#7f8c8d" />
          <Text style={styles.detailLabel}>Prescribed by:</Text>
          <Text style={styles.detailValue}>{medication.prescribedBy}</Text>
        </View>
      )}

      <View style={styles.dateContainer}>
        <View style={styles.dateItem}>
          <Text style={styles.dateLabel}>Started</Text>
          <Text style={styles.dateValue}>
            {new Date(medication.startDate).toLocaleDateString()}
          </Text>
        </View>
        {medication.endDate && (
          <View style={styles.dateItem}>
            <Text style={styles.dateLabel}>Until</Text>
            <Text style={styles.dateValue}>
              {new Date(medication.endDate).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>

      {medication.notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText}>{medication.notes}</Text>
        </View>
      )}
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#7f8c8d',
    marginTop: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 100,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#7f8c8d',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#95a5a6',
    textAlign: 'center',
  },
  medicationCard: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  medicationTitleContainer: {
    flex: 1,
  },
  medicationName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  typeTag: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  medicationDetails: {
    marginTop: 10,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#7f8c8d',
    marginLeft: 8,
    marginRight: 8,
    fontWeight: '600',
  },
  detailValue: {
    fontSize: 14,
    color: '#2c3e50',
    flex: 1,
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
  },
  dateItem: {
    alignItems: 'center',
  },
  dateLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  dateValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
  },
  notesContainer: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 5,
  },
  notesText: {
    fontSize: 14,
    color: '#2c3e50',
    lineHeight: 20,
  },
});

export default PrescriptionScreen;
