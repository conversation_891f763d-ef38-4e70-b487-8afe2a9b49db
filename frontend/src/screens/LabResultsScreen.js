import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { patientAPI, labResultsAPI } from '../services/api';

const LabResultsScreen = ({ navigation }) => {
  const [currentLabResults, setCurrentLabResults] = useState(null);
  const [formData, setFormData] = useState({
    hemoglobinLevel: '',
    bloodGlucoseLevel: '',
    ldlCholesterolLevel: '',
    serumCreatinineLevel: '',
    labName: '',
    orderedBy: '',
    notes: '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCurrentLabResults();
  }, []);

  const loadCurrentLabResults = async () => {
    try {
      const response = await patientAPI.getCurrentLabResults();
      setCurrentLabResults(response.data);
    } catch (error) {
      console.error('Error loading current lab results:', error);
      // It's okay if there are no current lab results
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const { hemoglobinLevel, bloodGlucoseLevel, ldlCholesterolLevel, serumCreatinineLevel } = formData;
    
    if (!hemoglobinLevel && !bloodGlucoseLevel && !ldlCholesterolLevel && !serumCreatinineLevel) {
      Alert.alert('Error', 'Please enter at least one lab result');
      return false;
    }

    // Validate ranges if values are provided
    if (hemoglobinLevel && (parseFloat(hemoglobinLevel) < 0 || parseFloat(hemoglobinLevel) > 25)) {
      Alert.alert('Error', 'Hemoglobin level should be between 0-25 g/dL');
      return false;
    }

    if (bloodGlucoseLevel && (parseFloat(bloodGlucoseLevel) < 0 || parseFloat(bloodGlucoseLevel) > 1000)) {
      Alert.alert('Error', 'Blood glucose level should be between 0-1000 mg/dL');
      return false;
    }

    if (ldlCholesterolLevel && (parseFloat(ldlCholesterolLevel) < 0 || parseFloat(ldlCholesterolLevel) > 500)) {
      Alert.alert('Error', 'LDL cholesterol level should be between 0-500 mg/dL');
      return false;
    }

    if (serumCreatinineLevel && (parseFloat(serumCreatinineLevel) < 0 || parseFloat(serumCreatinineLevel) > 20)) {
      Alert.alert('Error', 'Serum creatinine level should be between 0-20 mg/dL');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const labResultsData = {
        hemoglobinLevel: formData.hemoglobinLevel ? parseFloat(formData.hemoglobinLevel) : null,
        bloodGlucoseLevel: formData.bloodGlucoseLevel ? parseFloat(formData.bloodGlucoseLevel) : null,
        ldlCholesterolLevel: formData.ldlCholesterolLevel ? parseFloat(formData.ldlCholesterolLevel) : null,
        serumCreatinineLevel: formData.serumCreatinineLevel ? parseFloat(formData.serumCreatinineLevel) : null,
        labName: formData.labName,
        orderedBy: formData.orderedBy,
        notes: formData.notes,
        recordedAt: new Date().toISOString(),
      };

      await labResultsAPI.create(labResultsData);
      
      Alert.alert('Success', 'Lab results recorded successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error saving lab results:', error);
      Alert.alert('Error', 'Failed to save lab results');
    } finally {
      setLoading(false);
    }
  };

  const getLabStatus = (type, value) => {
    if (!value) return { status: 'Unknown', color: '#95a5a6' };
    
    switch (type) {
      case 'glucose':
        if (value < 70) return { status: 'Low', color: '#3498db' };
        else if (value <= 99) return { status: 'Normal', color: '#2ecc71' };
        else if (value <= 125) return { status: 'Prediabetes', color: '#f39c12' };
        else return { status: 'Diabetes', color: '#e74c3c' };
      
      case 'hemoglobin':
        if (value < 12.0) return { status: 'Low', color: '#e74c3c' };
        else if (value <= 16.0) return { status: 'Normal', color: '#2ecc71' };
        else return { status: 'High', color: '#f39c12' };
      
      case 'ldl':
        if (value < 100) return { status: 'Optimal', color: '#2ecc71' };
        else if (value <= 129) return { status: 'Near Optimal', color: '#f39c12' };
        else if (value <= 159) return { status: 'Borderline High', color: '#e67e22' };
        else return { status: 'High', color: '#e74c3c' };
      
      case 'creatinine':
        if (value <= 1.2) return { status: 'Normal', color: '#2ecc71' };
        else if (value <= 2.0) return { status: 'Mildly Elevated', color: '#f39c12' };
        else if (value <= 5.0) return { status: 'Moderately Elevated', color: '#e67e22' };
        else return { status: 'Severely Elevated', color: '#e74c3c' };
      
      default:
        return { status: 'Normal', color: '#2ecc71' };
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView}>
        {/* Current Lab Results */}
        {currentLabResults && (
          <View style={styles.currentResultsSection}>
            <Text style={styles.sectionTitle}>Current Lab Results</Text>
            <View style={styles.currentResultsGrid}>
              <LabCard
                title="Blood Glucose"
                value={currentLabResults.bloodGlucoseLevel}
                unit="mg/dL"
                status={getLabStatus('glucose', currentLabResults.bloodGlucoseLevel)}
              />
              <LabCard
                title="Hemoglobin"
                value={currentLabResults.hemoglobinLevel}
                unit="g/dL"
                status={getLabStatus('hemoglobin', currentLabResults.hemoglobinLevel)}
              />
              <LabCard
                title="LDL Cholesterol"
                value={currentLabResults.ldlCholesterolLevel}
                unit="mg/dL"
                status={getLabStatus('ldl', currentLabResults.ldlCholesterolLevel)}
              />
              <LabCard
                title="Serum Creatinine"
                value={currentLabResults.serumCreatinineLevel}
                unit="mg/dL"
                status={getLabStatus('creatinine', currentLabResults.serumCreatinineLevel)}
              />
            </View>
            <Text style={styles.recordedDate}>
              Recorded: {new Date(currentLabResults.recordedAt).toLocaleString()}
            </Text>
          </View>
        )}

        {/* New Lab Results Form */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Add New Lab Results</Text>
          
          {/* Lab Information */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Lab Information</Text>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Laboratory Name</Text>
              <TextInput
                style={styles.input}
                value={formData.labName}
                onChangeText={(value) => handleInputChange('labName', value)}
                placeholder="Enter lab name"
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Ordered By (Doctor)</Text>
              <TextInput
                style={styles.input}
                value={formData.orderedBy}
                onChangeText={(value) => handleInputChange('orderedBy', value)}
                placeholder="Doctor who ordered the test"
              />
            </View>
          </View>

          {/* Blood Glucose */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Blood Glucose Level</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.bloodGlucoseLevel}
                onChangeText={(value) => handleInputChange('bloodGlucoseLevel', value)}
                placeholder="90"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>mg/dL</Text>
            </View>
            {formData.bloodGlucoseLevel && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getLabStatus('glucose', parseFloat(formData.bloodGlucoseLevel)).color }
                ]}>
                  {getLabStatus('glucose', parseFloat(formData.bloodGlucoseLevel)).status}
                </Text>
              </View>
            )}
          </View>

          {/* Hemoglobin */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Hemoglobin Level</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.hemoglobinLevel}
                onChangeText={(value) => handleInputChange('hemoglobinLevel', value)}
                placeholder="14.0"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>g/dL</Text>
            </View>
            {formData.hemoglobinLevel && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getLabStatus('hemoglobin', parseFloat(formData.hemoglobinLevel)).color }
                ]}>
                  {getLabStatus('hemoglobin', parseFloat(formData.hemoglobinLevel)).status}
                </Text>
              </View>
            )}
          </View>

          {/* LDL Cholesterol */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>LDL Cholesterol Level</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.ldlCholesterolLevel}
                onChangeText={(value) => handleInputChange('ldlCholesterolLevel', value)}
                placeholder="100"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>mg/dL</Text>
            </View>
            {formData.ldlCholesterolLevel && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getLabStatus('ldl', parseFloat(formData.ldlCholesterolLevel)).color }
                ]}>
                  {getLabStatus('ldl', parseFloat(formData.ldlCholesterolLevel)).status}
                </Text>
              </View>
            )}
          </View>

          {/* Serum Creatinine */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Serum Creatinine Level</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.serumCreatinineLevel}
                onChangeText={(value) => handleInputChange('serumCreatinineLevel', value)}
                placeholder="1.0"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>mg/dL</Text>
            </View>
            {formData.serumCreatinineLevel && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getLabStatus('creatinine', parseFloat(formData.serumCreatinineLevel)).color }
                ]}>
                  {getLabStatus('creatinine', parseFloat(formData.serumCreatinineLevel)).status}
                </Text>
              </View>
            )}
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Notes (Optional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              placeholder="Any additional notes about the lab results..."
              multiline
              numberOfLines={3}
            />
          </View>

          <TouchableOpacity
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={loading}
          >
            <Ionicons name="save-outline" size={20} color="#ffffff" />
            <Text style={styles.saveButtonText}>
              {loading ? 'Saving...' : 'Save Lab Results'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const LabCard = ({ title, value, unit, status }) => (
  <View style={styles.labCard}>
    <Text style={styles.labTitle}>{title}</Text>
    <Text style={styles.labValue}>
      {value} <Text style={styles.labUnit}>{unit}</Text>
    </Text>
    <Text style={[styles.labStatus, { color: status.color }]}>{status.status}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  currentResultsSection: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formSection: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  currentResultsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  labCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  labTitle: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 8,
    fontWeight: '600',
  },
  labValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  labUnit: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  labStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  recordedDate: {
    fontSize: 14,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 15,
    fontStyle: 'italic',
  },
  inputGroup: {
    marginBottom: 25,
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
  },
  inputContainer: {
    marginBottom: 15,
  },
  singleInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
    textAlign: 'center',
    minWidth: 100,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
    textAlign: 'left',
    minWidth: '100%',
  },
  unit: {
    fontSize: 14,
    color: '#7f8c8d',
    marginLeft: 10,
    fontWeight: '600',
  },
  statusIndicator: {
    alignItems: 'center',
    marginTop: 10,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#3498db',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
  },
  saveButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default LabResultsScreen;
