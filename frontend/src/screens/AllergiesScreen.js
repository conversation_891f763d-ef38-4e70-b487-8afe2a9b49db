import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { patientAPI } from '../services/api';

const AllergiesScreen = ({ navigation }) => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const response = await patientAPI.getProfile();
      setProfile(response.data);
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProfile();
    setRefreshing(false);
  };

  const parseAllergies = (allergiesString) => {
    if (!allergiesString) return [];
    
    // Split by common delimiters and clean up
    return allergiesString
      .split(/[,;|\n]/)
      .map(allergy => allergy.trim())
      .filter(allergy => allergy.length > 0);
  };

  const getAllergySeverity = (allergy) => {
    const lowerAllergy = allergy.toLowerCase();
    
    // High severity allergies
    if (lowerAllergy.includes('anaphylaxis') || 
        lowerAllergy.includes('severe') ||
        lowerAllergy.includes('life-threatening') ||
        lowerAllergy.includes('epinephrine') ||
        lowerAllergy.includes('epipen')) {
      return { level: 'High', color: '#e74c3c', icon: 'warning' };
    }
    
    // Medium severity allergies
    if (lowerAllergy.includes('moderate') ||
        lowerAllergy.includes('swelling') ||
        lowerAllergy.includes('breathing') ||
        lowerAllergy.includes('hives')) {
      return { level: 'Medium', color: '#f39c12', icon: 'alert-circle' };
    }
    
    // Low severity (default)
    return { level: 'Mild', color: '#3498db', icon: 'information-circle' };
  };

  const getAllergyType = (allergy) => {
    const lowerAllergy = allergy.toLowerCase();
    
    if (lowerAllergy.includes('food') || 
        lowerAllergy.includes('peanut') ||
        lowerAllergy.includes('shellfish') ||
        lowerAllergy.includes('dairy') ||
        lowerAllergy.includes('egg') ||
        lowerAllergy.includes('soy') ||
        lowerAllergy.includes('wheat') ||
        lowerAllergy.includes('nut')) {
      return { type: 'Food', icon: 'restaurant', color: '#e67e22' };
    }
    
    if (lowerAllergy.includes('drug') ||
        lowerAllergy.includes('medication') ||
        lowerAllergy.includes('penicillin') ||
        lowerAllergy.includes('aspirin') ||
        lowerAllergy.includes('antibiotic')) {
      return { type: 'Drug', icon: 'medical', color: '#e74c3c' };
    }
    
    if (lowerAllergy.includes('environmental') ||
        lowerAllergy.includes('pollen') ||
        lowerAllergy.includes('dust') ||
        lowerAllergy.includes('mold') ||
        lowerAllergy.includes('pet') ||
        lowerAllergy.includes('seasonal')) {
      return { type: 'Environmental', icon: 'leaf', color: '#27ae60' };
    }
    
    if (lowerAllergy.includes('latex') ||
        lowerAllergy.includes('rubber') ||
        lowerAllergy.includes('contact')) {
      return { type: 'Contact', icon: 'hand-left', color: '#8e44ad' };
    }
    
    return { type: 'Other', icon: 'help-circle', color: '#95a5a6' };
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading allergies...</Text>
      </View>
    );
  }

  const allergies = parseAllergies(profile?.allergies);

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Allergies & Reactions</Text>
        <Text style={styles.headerSubtitle}>
          {allergies.length} known allergie{allergies.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Emergency Alert */}
      {allergies.some(allergy => getAllergySeverity(allergy).level === 'High') && (
        <View style={styles.emergencyAlert}>
          <Ionicons name="warning" size={24} color="#ffffff" />
          <View style={styles.emergencyText}>
            <Text style={styles.emergencyTitle}>SEVERE ALLERGIES PRESENT</Text>
            <Text style={styles.emergencySubtitle}>
              This patient has severe allergies. Check all medications and treatments.
            </Text>
          </View>
        </View>
      )}

      {allergies.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="shield-checkmark-outline" size={80} color="#2ecc71" />
          <Text style={styles.emptyTitle}>No Known Allergies</Text>
          <Text style={styles.emptySubtitle}>
            No allergies have been recorded in your profile
          </Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('EditProfile', { profile })}
          >
            <Text style={styles.addButtonText}>Update Profile</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          {/* Allergy Categories */}
          <View style={styles.categoriesContainer}>
            <Text style={styles.sectionTitle}>Allergy Categories</Text>
            <View style={styles.categoriesGrid}>
              {['Food', 'Drug', 'Environmental', 'Contact'].map(category => {
                const categoryAllergies = allergies.filter(
                  allergy => getAllergyType(allergy).type === category
                );
                const categoryInfo = getAllergyType(category.toLowerCase());
                
                return (
                  <View key={category} style={styles.categoryCard}>
                    <Ionicons 
                      name={categoryInfo.icon} 
                      size={24} 
                      color={categoryInfo.color} 
                    />
                    <Text style={styles.categoryName}>{category}</Text>
                    <Text style={styles.categoryCount}>
                      {categoryAllergies.length}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>

          {/* Allergy List */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>All Allergies</Text>
            {allergies.map((allergy, index) => {
              const severity = getAllergySeverity(allergy);
              const type = getAllergyType(allergy);
              
              return (
                <AllergyCard
                  key={index}
                  allergy={allergy}
                  severity={severity}
                  type={type}
                />
              );
            })}
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigation.navigate('EditProfile', { profile })}
            >
              <Ionicons name="create-outline" size={24} color="#e74c3c" />
              <Text style={styles.actionButtonText}>Update Allergies</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </ScrollView>
  );
};

const AllergyCard = ({ allergy, severity, type }) => (
  <View style={styles.allergyCard}>
    <View style={styles.allergyHeader}>
      <View style={styles.allergyInfo}>
        <Text style={styles.allergyName}>{allergy}</Text>
        <View style={styles.allergyTags}>
          <View style={[styles.typeTag, { backgroundColor: type.color }]}>
            <Ionicons name={type.icon} size={12} color="#ffffff" />
            <Text style={styles.typeText}>{type.type}</Text>
          </View>
          <View style={[styles.severityTag, { backgroundColor: severity.color }]}>
            <Ionicons name={severity.icon} size={12} color="#ffffff" />
            <Text style={styles.severityText}>{severity.level}</Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#7f8c8d',
    marginTop: 5,
  },
  emergencyAlert: {
    backgroundColor: '#e74c3c',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  emergencyText: {
    marginLeft: 15,
    flex: 1,
  },
  emergencyTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emergencySubtitle: {
    color: '#ffffff',
    fontSize: 14,
    marginTop: 2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 100,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2ecc71',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#95a5a6',
    textAlign: 'center',
    marginBottom: 30,
  },
  addButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  addButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },
  categoriesContainer: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  categoriesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryCard: {
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    width: '22%',
  },
  categoryName: {
    fontSize: 12,
    color: '#2c3e50',
    marginTop: 8,
    fontWeight: '600',
    textAlign: 'center',
  },
  categoryCount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginTop: 4,
  },
  section: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  allergyCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  allergyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  allergyInfo: {
    flex: 1,
  },
  allergyName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  allergyTags: {
    flexDirection: 'row',
  },
  typeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  severityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  severityText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  actionButtonText: {
    color: '#e74c3c',
    marginLeft: 8,
    fontWeight: '600',
  },
});

export default AllergiesScreen;
