import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { patientAPI, vitalSignsAPI } from '../services/api';

const VitalSignsScreen = ({ navigation }) => {
  const [currentVitals, setCurrentVitals] = useState(null);
  const [formData, setFormData] = useState({
    systolicBP: '',
    diastolicBP: '',
    pulseRate: '',
    respiratoryRate: '',
    notes: '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCurrentVitals();
  }, []);

  const loadCurrentVitals = async () => {
    try {
      const response = await patientAPI.getCurrentVitalSigns();
      setCurrentVitals(response.data);
    } catch (error) {
      console.error('Error loading current vitals:', error);
      // It's okay if there are no current vitals
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const { systolicBP, diastolicBP, pulseRate, respiratoryRate } = formData;
    
    if (!systolicBP || !diastolicBP || !pulseRate || !respiratoryRate) {
      Alert.alert('Error', 'Please fill in all vital signs');
      return false;
    }

    const systolic = parseFloat(systolicBP);
    const diastolic = parseFloat(diastolicBP);
    const pulse = parseInt(pulseRate);
    const respiratory = parseInt(respiratoryRate);

    if (systolic < 60 || systolic > 300) {
      Alert.alert('Error', 'Systolic BP should be between 60-300 mmHg');
      return false;
    }

    if (diastolic < 40 || diastolic > 200) {
      Alert.alert('Error', 'Diastolic BP should be between 40-200 mmHg');
      return false;
    }

    if (pulse < 30 || pulse > 220) {
      Alert.alert('Error', 'Pulse rate should be between 30-220 bpm');
      return false;
    }

    if (respiratory < 8 || respiratory > 60) {
      Alert.alert('Error', 'Respiratory rate should be between 8-60 breaths/min');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const vitalSignsData = {
        systolicBP: parseFloat(formData.systolicBP),
        diastolicBP: parseFloat(formData.diastolicBP),
        pulseRate: parseInt(formData.pulseRate),
        respiratoryRate: parseInt(formData.respiratoryRate),
        notes: formData.notes,
        recordedAt: new Date().toISOString(),
      };

      await vitalSignsAPI.create(vitalSignsData);
      
      Alert.alert('Success', 'Vital signs recorded successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error saving vital signs:', error);
      Alert.alert('Error', 'Failed to save vital signs');
    } finally {
      setLoading(false);
    }
  };

  const getBPCategory = (systolic, diastolic) => {
    if (!systolic || !diastolic) return 'Unknown';
    
    if (systolic < 120 && diastolic < 80) return 'Optimal';
    else if (systolic <= 129 && diastolic <= 84) return 'Normal';
    else if ((systolic >= 130 && systolic <= 139) || (diastolic >= 85 && diastolic <= 89)) return 'High Normal';
    else if ((systolic >= 140 && systolic <= 159) || (diastolic >= 90 && diastolic <= 99)) return 'Grade 1 Hypertension';
    else if ((systolic >= 160 && systolic <= 179) || (diastolic >= 100 && diastolic <= 109)) return 'Grade 2 Hypertension';
    else return 'Severe Hypertension';
  };

  const getBPColor = (category) => {
    if (category.includes('Optimal') || category.includes('Normal')) return '#2ecc71';
    else if (category.includes('High Normal')) return '#f39c12';
    else return '#e74c3c';
  };

  const getPulseStatus = (pulse) => {
    if (!pulse) return { status: 'Unknown', color: '#95a5a6' };
    const rate = parseInt(pulse);
    if (rate >= 60 && rate <= 100) return { status: 'Normal', color: '#2ecc71' };
    else if (rate < 60) return { status: 'Bradycardia', color: '#3498db' };
    else return { status: 'Tachycardia', color: '#e74c3c' };
  };

  const getRespiratoryStatus = (respiratory) => {
    if (!respiratory) return { status: 'Unknown', color: '#95a5a6' };
    const rate = parseInt(respiratory);
    if (rate >= 12 && rate <= 20) return { status: 'Normal', color: '#2ecc71' };
    else if (rate < 12) return { status: 'Bradypnea', color: '#3498db' };
    else return { status: 'Tachypnea', color: '#e74c3c' };
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView}>
        {/* Current Vitals */}
        {currentVitals && (
          <View style={styles.currentVitalsSection}>
            <Text style={styles.sectionTitle}>Current Vital Signs</Text>
            <View style={styles.currentVitalsGrid}>
              <VitalCard
                icon="heart"
                title="Blood Pressure"
                value={`${currentVitals.systolicBP}/${currentVitals.diastolicBP}`}
                unit="mmHg"
                status={getBPCategory(currentVitals.systolicBP, currentVitals.diastolicBP)}
                color={getBPColor(getBPCategory(currentVitals.systolicBP, currentVitals.diastolicBP))}
              />
              <VitalCard
                icon="pulse"
                title="Pulse Rate"
                value={currentVitals.pulseRate}
                unit="bpm"
                status={getPulseStatus(currentVitals.pulseRate).status}
                color={getPulseStatus(currentVitals.pulseRate).color}
              />
              <VitalCard
                icon="fitness"
                title="Respiratory Rate"
                value={currentVitals.respiratoryRate}
                unit="breaths/min"
                status={getRespiratoryStatus(currentVitals.respiratoryRate).status}
                color={getRespiratoryStatus(currentVitals.respiratoryRate).color}
              />
            </View>
            <Text style={styles.recordedDate}>
              Recorded: {new Date(currentVitals.recordedAt).toLocaleString()}
            </Text>
          </View>
        )}

        {/* New Vitals Form */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Record New Vital Signs</Text>
          
          {/* Blood Pressure */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Blood Pressure</Text>
            <View style={styles.bpContainer}>
              <View style={styles.bpInput}>
                <Text style={styles.label}>Systolic</Text>
                <TextInput
                  style={styles.input}
                  value={formData.systolicBP}
                  onChangeText={(value) => handleInputChange('systolicBP', value)}
                  placeholder="120"
                  keyboardType="numeric"
                />
                <Text style={styles.unit}>mmHg</Text>
              </View>
              <Text style={styles.bpSeparator}>/</Text>
              <View style={styles.bpInput}>
                <Text style={styles.label}>Diastolic</Text>
                <TextInput
                  style={styles.input}
                  value={formData.diastolicBP}
                  onChangeText={(value) => handleInputChange('diastolicBP', value)}
                  placeholder="80"
                  keyboardType="numeric"
                />
                <Text style={styles.unit}>mmHg</Text>
              </View>
            </View>
            {formData.systolicBP && formData.diastolicBP && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getBPColor(getBPCategory(parseFloat(formData.systolicBP), parseFloat(formData.diastolicBP))) }
                ]}>
                  {getBPCategory(parseFloat(formData.systolicBP), parseFloat(formData.diastolicBP))}
                </Text>
              </View>
            )}
          </View>

          {/* Pulse Rate */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Pulse Rate (Resting)</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.pulseRate}
                onChangeText={(value) => handleInputChange('pulseRate', value)}
                placeholder="72"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>bpm</Text>
            </View>
            {formData.pulseRate && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getPulseStatus(formData.pulseRate).color }
                ]}>
                  {getPulseStatus(formData.pulseRate).status}
                </Text>
              </View>
            )}
          </View>

          {/* Respiratory Rate */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Respiratory Rate (Resting)</Text>
            <View style={styles.singleInputContainer}>
              <TextInput
                style={styles.input}
                value={formData.respiratoryRate}
                onChangeText={(value) => handleInputChange('respiratoryRate', value)}
                placeholder="16"
                keyboardType="numeric"
              />
              <Text style={styles.unit}>breaths/min</Text>
            </View>
            {formData.respiratoryRate && (
              <View style={styles.statusIndicator}>
                <Text style={[
                  styles.statusText,
                  { color: getRespiratoryStatus(formData.respiratoryRate).color }
                ]}>
                  {getRespiratoryStatus(formData.respiratoryRate).status}
                </Text>
              </View>
            )}
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={styles.groupTitle}>Notes (Optional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(value) => handleInputChange('notes', value)}
              placeholder="Any additional notes about the measurements..."
              multiline
              numberOfLines={3}
            />
          </View>

          <TouchableOpacity
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={loading}
          >
            <Ionicons name="save-outline" size={20} color="#ffffff" />
            <Text style={styles.saveButtonText}>
              {loading ? 'Saving...' : 'Save Vital Signs'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const VitalCard = ({ icon, title, value, unit, status, color }) => (
  <View style={styles.vitalCard}>
    <Ionicons name={icon} size={24} color={color} />
    <Text style={styles.vitalTitle}>{title}</Text>
    <Text style={styles.vitalValue}>
      {value} <Text style={styles.vitalUnit}>{unit}</Text>
    </Text>
    <Text style={[styles.vitalStatus, { color }]}>{status}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  currentVitalsSection: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formSection: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  currentVitalsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vitalCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  vitalTitle: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  vitalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
  },
  vitalUnit: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  vitalStatus: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  recordedDate: {
    fontSize: 14,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 15,
    fontStyle: 'italic',
  },
  inputGroup: {
    marginBottom: 25,
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
  },
  bpContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bpInput: {
    flex: 1,
    alignItems: 'center',
  },
  bpSeparator: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginHorizontal: 15,
  },
  singleInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
    textAlign: 'center',
    minWidth: 80,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
    textAlign: 'left',
    minWidth: '100%',
  },
  unit: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 5,
    fontWeight: '600',
  },
  statusIndicator: {
    alignItems: 'center',
    marginTop: 10,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#e74c3c',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
  },
  saveButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default VitalSignsScreen;
