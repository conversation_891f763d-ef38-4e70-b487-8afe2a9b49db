import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { patientAPI } from '../services/api';

const { width: screenWidth } = Dimensions.get('window');

const ReportsScreen = ({ navigation }) => {
  const [vitalSigns, setVitalSigns] = useState(null);
  const [labResults, setLabResults] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadReportsData();
  }, []);

  const loadReportsData = async () => {
    try {
      const [vitalResponse, labResponse, profileResponse] = await Promise.all([
        patientAPI.getCurrentVitalSigns(),
        patientAPI.getCurrentLabResults(),
        patientAPI.getProfile(),
      ]);
      
      setVitalSigns(vitalResponse.data);
      setLabResults(labResponse.data);
      setProfile(profileResponse.data);
    } catch (error) {
      console.error('Error loading reports data:', error);
      Alert.alert('Error', 'Failed to load reports data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportsData();
    setRefreshing(false);
  };

  const getBMI = () => {
    if (profile?.height && profile?.weight) {
      const heightInMeters = profile.height / 100;
      const bmi = profile.weight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return null;
  };

  const getBMICategory = () => {
    const bmi = parseFloat(getBMI());
    if (isNaN(bmi)) return 'Unknown';
    
    if (bmi < 18.5) return 'Underweight';
    else if (bmi < 25) return 'Normal';
    else if (bmi < 30) return 'Overweight';
    else return 'Obese';
  };

  const getBMIColor = () => {
    const category = getBMICategory();
    switch (category) {
      case 'Normal': return '#2ecc71';
      case 'Underweight': return '#3498db';
      case 'Overweight': return '#f39c12';
      case 'Obese': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  const getBPStatus = () => {
    if (!vitalSigns?.systolicBP || !vitalSigns?.diastolicBP) return 'Unknown';
    
    const systolic = vitalSigns.systolicBP;
    const diastolic = vitalSigns.diastolicBP;
    
    if (systolic < 120 && diastolic < 80) return 'Optimal';
    else if (systolic <= 129 && diastolic <= 84) return 'Normal';
    else if ((systolic >= 130 && systolic <= 139) || (diastolic >= 85 && diastolic <= 89)) return 'High Normal';
    else if ((systolic >= 140 && systolic <= 159) || (diastolic >= 90 && diastolic <= 99)) return 'Grade 1 Hypertension';
    else if ((systolic >= 160 && systolic <= 179) || (diastolic >= 100 && diastolic <= 109)) return 'Grade 2 Hypertension';
    else return 'Severe Hypertension';
  };

  const getBPColor = () => {
    const status = getBPStatus();
    if (status.includes('Optimal') || status.includes('Normal')) return '#2ecc71';
    else if (status.includes('High Normal')) return '#f39c12';
    else return '#e74c3c';
  };

  const getLabStatus = (type, value) => {
    if (!value) return { status: 'Unknown', color: '#95a5a6' };
    
    switch (type) {
      case 'glucose':
        if (value < 70) return { status: 'Low', color: '#3498db' };
        else if (value <= 99) return { status: 'Normal', color: '#2ecc71' };
        else if (value <= 125) return { status: 'Prediabetes', color: '#f39c12' };
        else return { status: 'Diabetes', color: '#e74c3c' };
      
      case 'hemoglobin':
        if (value < 12.0) return { status: 'Low', color: '#e74c3c' };
        else if (value <= 16.0) return { status: 'Normal', color: '#2ecc71' };
        else return { status: 'High', color: '#f39c12' };
      
      case 'ldl':
        if (value < 100) return { status: 'Optimal', color: '#2ecc71' };
        else if (value <= 129) return { status: 'Near Optimal', color: '#f39c12' };
        else if (value <= 159) return { status: 'Borderline High', color: '#e67e22' };
        else return { status: 'High', color: '#e74c3c' };
      
      default:
        return { status: 'Normal', color: '#2ecc71' };
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading reports...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Health Reports</Text>
        <Text style={styles.headerSubtitle}>Your health metrics overview</Text>
      </View>

      {/* BMI Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Body Mass Index (BMI)</Text>
        {getBMI() ? (
          <View style={styles.bmiContainer}>
            <View style={styles.bmiCircle}>
              <Text style={[styles.bmiValue, { color: getBMIColor() }]}>
                {getBMI()}
              </Text>
              <Text style={styles.bmiUnit}>kg/m²</Text>
            </View>
            <View style={styles.bmiInfo}>
              <Text style={[styles.bmiCategory, { color: getBMIColor() }]}>
                {getBMICategory()}
              </Text>
              <Text style={styles.bmiDetails}>
                Height: {profile.height} cm • Weight: {profile.weight} kg
              </Text>
            </View>
          </View>
        ) : (
          <Text style={styles.noDataText}>No height/weight data available</Text>
        )}
      </View>

      {/* Vital Signs Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Latest Vital Signs</Text>
        {vitalSigns ? (
          <View style={styles.vitalsGrid}>
            <VitalCard
              icon="heart"
              title="Blood Pressure"
              value={`${vitalSigns.systolicBP}/${vitalSigns.diastolicBP}`}
              unit="mmHg"
              status={getBPStatus()}
              color={getBPColor()}
            />
            <VitalCard
              icon="pulse"
              title="Pulse Rate"
              value={vitalSigns.pulseRate}
              unit="bpm"
              status={vitalSigns.pulseRate >= 60 && vitalSigns.pulseRate <= 100 ? 'Normal' : 'Abnormal'}
              color={vitalSigns.pulseRate >= 60 && vitalSigns.pulseRate <= 100 ? '#2ecc71' : '#e74c3c'}
            />
            <VitalCard
              icon="fitness"
              title="Respiratory Rate"
              value={vitalSigns.respiratoryRate}
              unit="breaths/min"
              status={vitalSigns.respiratoryRate >= 12 && vitalSigns.respiratoryRate <= 20 ? 'Normal' : 'Abnormal'}
              color={vitalSigns.respiratoryRate >= 12 && vitalSigns.respiratoryRate <= 20 ? '#2ecc71' : '#e74c3c'}
            />
          </View>
        ) : (
          <Text style={styles.noDataText}>No vital signs data available</Text>
        )}
      </View>

      {/* Lab Results Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Latest Lab Results</Text>
        {labResults ? (
          <View style={styles.labGrid}>
            <LabCard
              title="Blood Glucose"
              value={labResults.bloodGlucoseLevel}
              unit="mg/dL"
              status={getLabStatus('glucose', labResults.bloodGlucoseLevel)}
            />
            <LabCard
              title="Hemoglobin"
              value={labResults.hemoglobinLevel}
              unit="g/dL"
              status={getLabStatus('hemoglobin', labResults.hemoglobinLevel)}
            />
            <LabCard
              title="LDL Cholesterol"
              value={labResults.ldlCholesterolLevel}
              unit="mg/dL"
              status={getLabStatus('ldl', labResults.ldlCholesterolLevel)}
            />
            <LabCard
              title="Serum Creatinine"
              value={labResults.serumCreatinineLevel}
              unit="mg/dL"
              status={{ status: 'Normal', color: '#2ecc71' }}
            />
          </View>
        ) : (
          <Text style={styles.noDataText}>No lab results data available</Text>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Update Records</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('VitalSigns')}
          >
            <Ionicons name="heart-outline" size={32} color="#e74c3c" />
            <Text style={styles.actionText}>Update Vitals</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('LabResults')}
          >
            <Ionicons name="flask-outline" size={32} color="#3498db" />
            <Text style={styles.actionText}>Add Lab Results</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const VitalCard = ({ icon, title, value, unit, status, color }) => (
  <View style={styles.vitalCard}>
    <Ionicons name={icon} size={24} color={color} />
    <Text style={styles.vitalTitle}>{title}</Text>
    <Text style={styles.vitalValue}>
      {value} <Text style={styles.vitalUnit}>{unit}</Text>
    </Text>
    <Text style={[styles.vitalStatus, { color }]}>{status}</Text>
  </View>
);

const LabCard = ({ title, value, unit, status }) => (
  <View style={styles.labCard}>
    <Text style={styles.labTitle}>{title}</Text>
    <Text style={styles.labValue}>
      {value} <Text style={styles.labUnit}>{unit}</Text>
    </Text>
    <Text style={[styles.labStatus, { color: status.color }]}>{status.status}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#7f8c8d',
    marginTop: 5,
  },
  section: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  noDataText: {
    fontSize: 16,
    color: '#95a5a6',
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  bmiContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bmiCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
  },
  bmiValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  bmiUnit: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  bmiInfo: {
    flex: 1,
  },
  bmiCategory: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  bmiDetails: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  vitalsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  vitalCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  vitalTitle: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  vitalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
  },
  vitalUnit: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  vitalStatus: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  labGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  labCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  labTitle: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 8,
    fontWeight: '600',
  },
  labValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  labUnit: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  labStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    width: '45%',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default ReportsScreen;
