import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { patientAPI } from '../services/api';

const HistoryScreen = () => {
  const [medicalHistory, setMedicalHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMedicalHistory();
  }, []);

  const loadMedicalHistory = async () => {
    try {
      const response = await patientAPI.getMedicalHistory();
      setMedicalHistory(response.data);
    } catch (error) {
      console.error('Error loading medical history:', error);
      Alert.alert('Error', 'Failed to load medical history');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadMedicalHistory();
    setRefreshing(false);
  };

  const getRecordTypeIcon = (type) => {
    switch (type) {
      case 'CONSULTATION':
        return 'person-outline';
      case 'LAB_RESULT':
        return 'flask-outline';
      case 'IMAGING':
        return 'scan-outline';
      case 'PROCEDURE':
        return 'medical-outline';
      case 'SURGERY':
        return 'cut-outline';
      case 'EMERGENCY_VISIT':
        return 'warning-outline';
      case 'HOSPITALIZATION':
        return 'bed-outline';
      case 'VACCINATION':
        return 'shield-checkmark-outline';
      case 'PRESCRIPTION':
        return 'receipt-outline';
      case 'FOLLOW_UP':
        return 'refresh-outline';
      case 'SPECIALIST_REFERRAL':
        return 'arrow-forward-outline';
      case 'PHYSICAL_EXAM':
        return 'fitness-outline';
      case 'DENTAL':
        return 'happy-outline';
      case 'MENTAL_HEALTH':
        return 'brain-outline';
      case 'PREVENTIVE_CARE':
        return 'shield-outline';
      default:
        return 'document-outline';
    }
  };

  const getRecordTypeColor = (type) => {
    switch (type) {
      case 'EMERGENCY_VISIT':
        return '#e74c3c';
      case 'SURGERY':
        return '#8e44ad';
      case 'HOSPITALIZATION':
        return '#e67e22';
      case 'LAB_RESULT':
        return '#3498db';
      case 'VACCINATION':
        return '#2ecc71';
      case 'CONSULTATION':
        return '#f39c12';
      default:
        return '#95a5a6';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading medical history...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Medical History</Text>
        <Text style={styles.headerSubtitle}>
          {medicalHistory.length} record{medicalHistory.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {medicalHistory.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-text-outline" size={80} color="#bdc3c7" />
          <Text style={styles.emptyTitle}>No Medical History</Text>
          <Text style={styles.emptySubtitle}>
            Your medical records will appear here
          </Text>
        </View>
      ) : (
        medicalHistory.map((record, index) => (
          <MedicalRecordCard
            key={record.id || index}
            record={record}
            icon={getRecordTypeIcon(record.type)}
            color={getRecordTypeColor(record.type)}
          />
        ))
      )}
    </ScrollView>
  );
};

const MedicalRecordCard = ({ record, icon, color }) => (
  <View style={styles.recordCard}>
    <View style={styles.recordHeader}>
      <View style={styles.recordTitleContainer}>
        <View style={styles.recordIconContainer}>
          <Ionicons name={icon} size={24} color={color} />
        </View>
        <View style={styles.recordTitleText}>
          <Text style={styles.recordType}>
            {record.type?.replace(/_/g, ' ') || 'Medical Record'}
          </Text>
          <Text style={styles.recordDate}>{formatDate(record.date)}</Text>
        </View>
      </View>
    </View>

    <View style={styles.recordContent}>
      <Text style={styles.recordDescription}>{record.description}</Text>
      
      {record.diagnosis && (
        <View style={styles.recordDetail}>
          <Text style={styles.detailLabel}>Diagnosis:</Text>
          <Text style={styles.detailValue}>{record.diagnosis}</Text>
        </View>
      )}

      {record.treatment && (
        <View style={styles.recordDetail}>
          <Text style={styles.detailLabel}>Treatment:</Text>
          <Text style={styles.detailValue}>{record.treatment}</Text>
        </View>
      )}

      {record.provider && (
        <View style={styles.recordDetail}>
          <Text style={styles.detailLabel}>Provider:</Text>
          <Text style={styles.detailValue}>{record.provider}</Text>
        </View>
      )}

      {record.facility && (
        <View style={styles.recordDetail}>
          <Text style={styles.detailLabel}>Facility:</Text>
          <Text style={styles.detailValue}>{record.facility}</Text>
        </View>
      )}

      {record.department && (
        <View style={styles.recordDetail}>
          <Text style={styles.detailLabel}>Department:</Text>
          <Text style={styles.detailValue}>{record.department}</Text>
        </View>
      )}

      {record.followUpDate && (
        <View style={styles.followUpContainer}>
          <Ionicons name="calendar-outline" size={16} color="#e74c3c" />
          <Text style={styles.followUpText}>
            Follow-up: {formatDate(record.followUpDate)}
          </Text>
        </View>
      )}

      {record.notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText}>{record.notes}</Text>
        </View>
      )}

      {record.cost && (
        <View style={styles.costContainer}>
          <Text style={styles.costLabel}>Cost:</Text>
          <Text style={styles.costValue}>${record.cost}</Text>
        </View>
      )}
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#7f8c8d',
    marginTop: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 100,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#7f8c8d',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#95a5a6',
    textAlign: 'center',
  },
  recordCard: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  recordHeader: {
    marginBottom: 15,
  },
  recordTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  recordTitleText: {
    flex: 1,
  },
  recordType: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    textTransform: 'capitalize',
  },
  recordDate: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 2,
  },
  recordContent: {
    marginTop: 10,
  },
  recordDescription: {
    fontSize: 16,
    color: '#2c3e50',
    lineHeight: 22,
    marginBottom: 15,
  },
  recordDetail: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    width: 80,
  },
  detailValue: {
    fontSize: 14,
    color: '#2c3e50',
    flex: 1,
  },
  followUpContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff5f5',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  followUpText: {
    fontSize: 14,
    color: '#e74c3c',
    marginLeft: 8,
    fontWeight: '600',
  },
  notesContainer: {
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
    marginBottom: 5,
  },
  notesText: {
    fontSize: 14,
    color: '#2c3e50',
    lineHeight: 20,
  },
  costContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
  },
  costLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#7f8c8d',
  },
  costValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
  },
});

export default HistoryScreen;
