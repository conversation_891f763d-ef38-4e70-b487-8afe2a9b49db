import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
// Note: For Expo, we'll use a simple modal picker instead of @react-native-picker/picker
import { Ionicons } from '@expo/vector-icons';
import { patientAPI } from '../services/api';

const EditProfileScreen = ({ navigation, route }) => {
  const { profile } = route.params;
  const [formData, setFormData] = useState({
    healthId: '',
    dateOfBirth: '',
    gender: '',
    phoneNumber: '',
    height: '',
    weight: '',
    waistCircumference: '',
    bloodGroup: '',
    allergies: '',
    smokingStatus: '',
    physicalActivityLevel: '',
    metScore: '',
    pastMedicalIllnesses: '',
    address: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: '',
    insuranceProvider: '',
    insurancePolicyNumber: '',
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (profile) {
      setFormData({
        healthId: profile.healthId || '',
        dateOfBirth: profile.dateOfBirth || '',
        gender: profile.gender || '',
        phoneNumber: profile.phoneNumber || '',
        height: profile.height?.toString() || '',
        weight: profile.weight?.toString() || '',
        waistCircumference: profile.waistCircumference?.toString() || '',
        bloodGroup: profile.bloodGroup || '',
        allergies: profile.allergies || '',
        smokingStatus: profile.smokingStatus || '',
        physicalActivityLevel: profile.physicalActivityLevel || '',
        metScore: profile.metScore?.toString() || '',
        pastMedicalIllnesses: profile.pastMedicalIllnesses || '',
        address: profile.address || '',
        emergencyContactName: profile.emergencyContactName || '',
        emergencyContactPhone: profile.emergencyContactPhone || '',
        emergencyContactRelation: profile.emergencyContactRelation || '',
        insuranceProvider: profile.insuranceProvider || '',
        insurancePolicyNumber: profile.insurancePolicyNumber || '',
      });
    }
  }, [profile]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Convert numeric fields
      const profileData = {
        ...formData,
        height: formData.height ? parseFloat(formData.height) : null,
        weight: formData.weight ? parseFloat(formData.weight) : null,
        waistCircumference: formData.waistCircumference ? parseFloat(formData.waistCircumference) : null,
        metScore: formData.metScore ? parseFloat(formData.metScore) : null,
      };

      await patientAPI.updateProfile(profileData);
      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView style={styles.scrollView}>
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Health ID</Text>
            <TextInput
              style={styles.input}
              value={formData.healthId}
              onChangeText={(value) => handleInputChange('healthId', value)}
              placeholder="Enter Health ID"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Date of Birth</Text>
            <TextInput
              style={styles.input}
              value={formData.dateOfBirth}
              onChangeText={(value) => handleInputChange('dateOfBirth', value)}
              placeholder="YYYY-MM-DD"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Gender</Text>
            <TextInput
              style={styles.input}
              value={formData.gender}
              onChangeText={(value) => handleInputChange('gender', value)}
              placeholder="MALE, FEMALE, OTHER, or PREFER_NOT_TO_SAY"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={styles.input}
              value={formData.phoneNumber}
              onChangeText={(value) => handleInputChange('phoneNumber', value)}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
            />
          </View>
        </View>

        {/* Physical Measurements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Physical Measurements</Text>
          
          <View style={styles.row}>
            <View style={styles.halfInput}>
              <Text style={styles.label}>Height (cm)</Text>
              <TextInput
                style={styles.input}
                value={formData.height}
                onChangeText={(value) => handleInputChange('height', value)}
                placeholder="Height"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.halfInput}>
              <Text style={styles.label}>Weight (kg)</Text>
              <TextInput
                style={styles.input}
                value={formData.weight}
                onChangeText={(value) => handleInputChange('weight', value)}
                placeholder="Weight"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Waist Circumference (cm)</Text>
            <TextInput
              style={styles.input}
              value={formData.waistCircumference}
              onChangeText={(value) => handleInputChange('waistCircumference', value)}
              placeholder="Waist circumference"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Blood Group</Text>
            <TextInput
              style={styles.input}
              value={formData.bloodGroup}
              onChangeText={(value) => handleInputChange('bloodGroup', value)}
              placeholder="A+, A-, B+, B-, AB+, AB-, O+, O-"
            />
          </View>
        </View>

        {/* Health Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Health Information</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Allergies</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.allergies}
              onChangeText={(value) => handleInputChange('allergies', value)}
              placeholder="List any allergies"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Smoking Status</Text>
            <TextInput
              style={styles.input}
              value={formData.smokingStatus}
              onChangeText={(value) => handleInputChange('smokingStatus', value)}
              placeholder="YES, NO, or FORMER_SMOKER"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Physical Activity Level</Text>
            <TextInput
              style={styles.input}
              value={formData.physicalActivityLevel}
              onChangeText={(value) => handleInputChange('physicalActivityLevel', value)}
              placeholder="SEDENTARY, LIGHTLY_ACTIVE, MODERATELY_ACTIVE, etc."
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>MET Score</Text>
            <TextInput
              style={styles.input}
              value={formData.metScore}
              onChangeText={(value) => handleInputChange('metScore', value)}
              placeholder="MET Score"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Past Medical Illnesses</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.pastMedicalIllnesses}
              onChangeText={(value) => handleInputChange('pastMedicalIllnesses', value)}
              placeholder="List past medical conditions"
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact & Emergency Information</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Address</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Enter address"
              multiline
              numberOfLines={2}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Emergency Contact Name</Text>
            <TextInput
              style={styles.input}
              value={formData.emergencyContactName}
              onChangeText={(value) => handleInputChange('emergencyContactName', value)}
              placeholder="Emergency contact name"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Emergency Contact Phone</Text>
            <TextInput
              style={styles.input}
              value={formData.emergencyContactPhone}
              onChangeText={(value) => handleInputChange('emergencyContactPhone', value)}
              placeholder="Emergency contact phone"
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Emergency Contact Relation</Text>
            <TextInput
              style={styles.input}
              value={formData.emergencyContactRelation}
              onChangeText={(value) => handleInputChange('emergencyContactRelation', value)}
              placeholder="Relationship (e.g., Spouse, Parent)"
            />
          </View>
        </View>

        {/* Insurance Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Insurance Information</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Insurance Provider</Text>
            <TextInput
              style={styles.input}
              value={formData.insuranceProvider}
              onChangeText={(value) => handleInputChange('insuranceProvider', value)}
              placeholder="Insurance company name"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Policy Number</Text>
            <TextInput
              style={styles.input}
              value={formData.insurancePolicyNumber}
              onChangeText={(value) => handleInputChange('insurancePolicyNumber', value)}
              placeholder="Insurance policy number"
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Saving...' : 'Save Profile'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#ffffff',
    margin: 15,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ecf0f1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },

  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
  },
  saveButton: {
    backgroundColor: '#e74c3c',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  saveButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default EditProfileScreen;
